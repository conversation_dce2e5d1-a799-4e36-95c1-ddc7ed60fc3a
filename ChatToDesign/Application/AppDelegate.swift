// AppDelegate.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/14.
//

import AppTrackingTransparency
import AppsFlyerLib
import FirebaseCore
import FirebaseMessaging
import GoogleSignIn
import UIKit
import UserNotifications

class AppDelegate: NSObject, UIApplicationDelegate {

  // URL handling is now managed by ChatToDesignApp.swift and DeepLinkService
  // This method is kept for compatibility but delegates to the service
  func application(
    _ app: UIApplication,
    open url: URL,
    options: [UIApplication.OpenURLOptionsKey: Any] = [:]
  ) -> Bool {
    let container = AppDependencyContainer.shared
    // TODO: 重构完成后使用 container.deepLinkService.handleURL(url)
    return container.deepLinkHandler.handleURL(url)
  }

  func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]? = nil
  ) -> Bool {
    // 配置 Firebase
    FirebaseApp.configure()

    // 初始化依赖容器
    let container = AppDependencyContainer.shared

    // 初始化远程配置
    // Task {
    //   do {
    //     try await container.configService.fetchConfig()
    //   } catch {
    //     Logger.error("Failed to fetch remote config: \(error)")
    //   }
    // }

    // 配置推送通知
    setupPushNotifications(application)

    // 初始化Sentry
    container.errorReportingService.setup()

    Logger.info("Firebase & Sentry initialized")

    // 设置通知代理
    UNUserNotificationCenter.current().delegate = self
    Messaging.messaging().delegate = self

    // 请求通知权限
    requestNotificationAuthorization(application: application)

    // 配置订阅模块（先以匿名用户配置，登录后再关联用户ID）
    Task {
      await container.subscriptionModule.configure()
      Logger.info("Subscription module initialized with anonymous user")
    }

    // 配置分析服务
    Task {
      await container.behaviorAnalyticsService.configure(with: .debug)
      Logger.info("Behavior analytics service (Firebase) initialized")
    }

    // 配置 AppsFlyer
    Task {
      do {
        try await container.configureAppsFlyerFromRemoteConfig()
        Logger.info("AppsFlyer attribution analytics initialized")
      } catch {
        Logger.error("Failed to initialize AppsFlyer: \(error)")
      }
    }

    // App lifecycle is now handled by ChatToDesignApp.swift using scenePhase monitoring

    // 配置 Google 登录
    GIDSignIn.sharedInstance.restorePreviousSignIn { user, error in
      if error != nil || user == nil {
        Logger.debug("未找到已登录用户")
      } else {
        Logger.info("用户已保持登录: \(user?.profile?.email ?? "")")
      }
    }

    Messaging.messaging().token { token, error in
      if let error = error {
        Logger.debug("Error fetching FCM registration token: \(error)")
      } else if let token = token {
        Logger.debug("FCM registration token: \(token)")
        //          self.fcmRegTokenMessage.text  = "Remote FCM registration token: \(token)"
      }
    }

    // printAvailableFonts()

    return true
  }

  func printAvailableFonts() {
    for family in UIFont.familyNames {
      print("Font Family: \(family)")
      for name in UIFont.fontNames(forFamilyName: family) {
        print("- \(name)")
      }
    }
  }

  // 请求通知权限
  private func requestNotificationAuthorization(application: UIApplication) {
    let authOptions: UNAuthorizationOptions = [.alert, .badge, .sound]
    UNUserNotificationCenter.current().requestAuthorization(options: authOptions) {
      granted, error in
      if granted {
        Logger.debug("通知权限已获取")
        DispatchQueue.main.async {
          application.registerForRemoteNotifications()
        }
      } else if let error = error {
        Logger.error("通知权限请求失败: \(error.localizedDescription)")
      }
    }
  }

  // 配置推送通知
  private func setupPushNotifications(_ application: UIApplication) {
    // 在主线程注册远程通知
    DispatchQueue.main.async {
      application.registerForRemoteNotifications()
    }
  }
}

// MARK: - UNUserNotificationCenterDelegate

extension AppDelegate: UNUserNotificationCenterDelegate {
  // 前台通知处理
  func userNotificationCenter(
    _ center: UNUserNotificationCenter,
    willPresent notification: UNNotification,
    withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
  ) {
    completionHandler([.banner, .sound])
  }

  // 通知响应处理
  func userNotificationCenter(
    _ center: UNUserNotificationCenter,
    didReceive response: UNNotificationResponse,
    withCompletionHandler completionHandler: @escaping () -> Void
  ) {
    let userInfo = response.notification.request.content.userInfo
    Logger.debug("收到通知响应: \(userInfo)")

    // 处理通知响应逻辑，例如跳转到特定页面

    completionHandler()
  }

  // Handle silent notifications (data messages)
  func application(
    _ application: UIApplication,
    didReceiveRemoteNotification userInfo: [AnyHashable: Any],
    fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void
  ) {
    Logger.log("收到静默通知/数据消息: \(userInfo)", level: .debug)
    completionHandler(.noData)
  }
}

// MARK: - MessagingDelegate

extension AppDelegate: MessagingDelegate {

  func messaging(_ messaging: Messaging, didReceiveRegistrationToken fcmToken: String?) {
    guard let token = fcmToken else { return }
    Logger.log("Firebase registration token: \(token)", level: .debug)
    // TODO: 更新 Firebase 注册令牌
  }
}

// MARK: - APNs 注册

extension AppDelegate {
  // 处理 APNs 注册成功
  func application(
    _ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data
  ) {
    // 将 APNs token 传递给 FCM
    Messaging.messaging().apnsToken = deviceToken
    Logger.log("APNs token: \(deviceToken)", level: .debug)
  }

  // 处理 APNs 注册失败
  func application(
    _ application: UIApplication,
    didFailToRegisterForRemoteNotificationsWithError error: Error
  ) {
    Logger.log("APNs 注册失败: \(error.localizedDescription)", level: .error)
  }
}

// AppsFlyer integration is now handled by AppsFlyerDeepLinkHandler
