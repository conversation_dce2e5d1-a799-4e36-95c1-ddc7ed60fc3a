//
//  ChatToDesignApp.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/14.
//

import SwiftUI

extension Notification.Name {
  static let showSubscriptionPage = Notification.Name("showSubscriptionPage")
  static let navigateToProfile = Notification.Name("navigateToProfile")
}

@main
struct ChatToDesignApp: App {
  // Register AppDelegate
  @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

  // Environment variables
  @Environment(\.scenePhase) private var scenePhase

  var body: some Scene {
    WindowGroup {
      ZStack {
        RootView()
      }
      .preferredColorScheme(.dark)
      .onOpenURL { url in
        let container = AppDependencyContainer.shared
        // TODO: 重构完成后使用 container.deepLinkService.handleURL(url)
        _ = container.deepLinkHandler.handleURL(url)
      }
      .onContinueUserActivity(NSUserActivityTypeBrowsingWeb) { userActivity in
        let container = AppDependencyContainer.shared
        // TODO: 重构完成后使用 container.deepLinkService.handleUniversalLink(userActivity)
        _ = container.deepLinkHandler.handleUniversalLink(userActivity)
      }
    }
    .onChange(of: scenePhase) { newPhase in
      switch newPhase {
      case .active:
        Logger.info("App: Scene became active")
        // Handle AppsFlyer SDK lifecycle
        let container = AppDependencyContainer.shared
        // TODO: 重构完成后使用 container.deepLinkService.handleAppBecameActive()
        container.deepLinkHandler.handleAppBecameActive()
      case .inactive:
        Logger.info("App: Scene became inactive")
      case .background:
        Logger.info("App: Scene moved to background")
      @unknown default:
        Logger.info("App: Scene changed to unknown state")
      }
    }
  }
}
