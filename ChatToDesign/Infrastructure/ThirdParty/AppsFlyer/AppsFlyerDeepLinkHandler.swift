//
//  AppsFlyerDeepLinkHandler.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/1/21.
//

import AppTrackingTransparency
import AppsFlyerLib
import Foundation
import GoogleSignIn

/// Handles AppsFlyer deep links, URL routing, and SDK lifecycle
public final class AppsFlyerDeepLinkHandler: ObservableObject {

  // MARK: - Properties

  /// Deep link data received from A<PERSON><PERSON>lyer
  @Published public var deepLinkData: [String: Any]?

  /// Indicates if a deep link is being processed
  @Published public var isProcessingDeepLink = false

  // MARK: - SDK Lifecycle Management

  /// Handle app becoming active - start AppsFlyer SDK
  public func handleAppBecameActive() {
    Logger.info("🚀 AppsFlyer: Starting SDK")
    AppsFlyerLib.shared().start()

    // Request ATT authorization on iOS 14+
    if #available(iOS 14, *) {
      requestATTPermission()
    }
  }

  /// Request App Tracking Transparency permission
  @available(iOS 14, *)
  private func requestATTPermission() {
    ATTrackingManager.requestTrackingAuthorization { [weak self] status in
      guard let self = self else { return }

      switch status {
      case .authorized:
        Logger.info("ATT: User authorized tracking")
      case .denied:
        Logger.info("ATT: User denied tracking")
      case .notDetermined:
        Logger.info("ATT: Tracking authorization not determined")
      case .restricted:
        Logger.info("ATT: Tracking authorization restricted")
      @unknown default:
        Logger.warning("ATT: Unknown tracking authorization status")
      }

      // Track ATT status for analytics
      let container = AppDependencyContainer.shared
      container.attributionAnalyticsService.track(
        name: "att_status_changed",
        parameters: ["status": self.attStatusString(status)],
        category: .privacy
      )
    }
  }

  /// Convert ATT status to string
  @available(iOS 14, *)
  private func attStatusString(_ status: ATTrackingManager.AuthorizationStatus) -> String {
    switch status {
    case .authorized: return "authorized"
    case .denied: return "denied"
    case .notDetermined: return "not_determined"
    case .restricted: return "restricted"
    @unknown default: return "unknown"
    }
  }

  // MARK: - URL Handling

  /// Handle URL schemes (including Google Sign-In and AppsFlyer deep links)
  /// - Parameter url: The URL to handle
  /// - Returns: True if the URL was handled successfully
  public func handleURL(_ url: URL) -> Bool {
    Logger.info("AppsFlyerDeepLinkHandler: Processing URL Scheme - \(url.absoluteString)")

    // Handle Google Sign-In URL first
    if GIDSignIn.sharedInstance.handle(url) {
      Logger.info("AppsFlyerDeepLinkHandler: Google Sign-In URL handled")
      return true
    }

    // Handle AppsFlyer deep links
    processDeepLink(from: url)
    Logger.info("AppsFlyerDeepLinkHandler: URL Scheme processed successfully")
    return true
  }

  /// Handle Universal Links
  /// - Parameter userActivity: The user activity containing the universal link
  /// - Returns: True if the universal link was handled successfully
  public func handleUniversalLink(_ userActivity: NSUserActivity) -> Bool {
    guard userActivity.activityType == NSUserActivityTypeBrowsingWeb,
      let url = userActivity.webpageURL
    else {
      Logger.warning("AppsFlyerDeepLinkHandler: Invalid user activity for Universal Link")
      return false
    }

    Logger.info("AppsFlyerDeepLinkHandler: Processing Universal Link - \(url.absoluteString)")

    // Notify AppsFlyer SDK
    AppsFlyerLib.shared().continue(userActivity, restorationHandler: nil)

    // Handle deep link routing
    processDeepLink(from: url)

    Logger.info("AppsFlyerDeepLinkHandler: Universal Link processed successfully")
    return true
  }

  // MARK: - Deep Link Processing

  /// Process deep link result from AppsFlyer
  /// - Parameter result: AppsFlyer deep link result
  public func processDeepLink(_ result: DeepLinkResult) {
    isProcessingDeepLink = true

    switch result.status {
    case .found:
      handleDeepLinkFound(result)
    case .notFound:
      handleDeepLinkNotFound()
    case .failure:
      handleDeepLinkFailure(result.error)
    @unknown default:
      handleUnknownDeepLinkStatus()
    }

    isProcessingDeepLink = false
  }

  /// Process deep link from URL (for manual deep link handling)
  /// - Parameter url: Deep link URL
  public func processDeepLink(from url: URL) {
    Logger.info("AppsFlyerDeepLinkHandler: Processing manual deep link - \(url)")

    let parameters = extractParameters(from: url)
    deepLinkData = parameters

    routeToDestination(from: parameters)
  }

  // MARK: - Private Deep Link Handlers

  private func handleDeepLinkFound(_ result: DeepLinkResult) {
    guard let deepLink = result.deepLink else {
      Logger.warning("AppsFlyerDeepLinkHandler: Deep link found but no data available")
      return
    }

    Logger.info("AppsFlyerDeepLinkHandler: Deep link found successfully")

    let clickEvent = deepLink.clickEvent
    deepLinkData = clickEvent

    // Extract routing information
    let parameters = extractDeepLinkParameters(from: clickEvent)

    // Route to appropriate destination
    routeToDestination(parameters: parameters)

    // Track deep link event
    trackDeepLinkEvent(parameters: parameters.allParameters, status: "found")
  }

  private func handleDeepLinkNotFound() {
    Logger.info("AppsFlyerDeepLinkHandler: Deep link not found")
    deepLinkData = nil

    // Track deep link event
    trackDeepLinkEvent(parameters: [:], status: "not_found")
  }

  private func handleDeepLinkFailure(_ error: Error?) {
    let errorMessage = error?.localizedDescription ?? "Unknown error"
    Logger.error("AppsFlyerDeepLinkHandler: Deep link processing failed - \(errorMessage)")

    deepLinkData = nil

    // Track deep link event
    trackDeepLinkEvent(parameters: ["error": errorMessage], status: "failure")
  }

  private func handleUnknownDeepLinkStatus() {
    Logger.warning("AppsFlyerDeepLinkHandler: Unknown deep link status")
    deepLinkData = nil

    // Track deep link event
    trackDeepLinkEvent(parameters: [:], status: "unknown")
  }

  // MARK: - Parameter Extraction

  private func extractDeepLinkParameters(from clickEvent: [String: Any]) -> DeepLinkParameters {
    let parameters = DeepLinkParameters()

    // Extract common parameters
    parameters.campaign = clickEvent["campaign"] as? String
    parameters.mediaSource = clickEvent["media_source"] as? String
    parameters.channel = clickEvent["channel"] as? String
    parameters.keywords = clickEvent["keywords"] as? String
    parameters.adGroup = clickEvent["adgroup"] as? String
    parameters.adSet = clickEvent["adset"] as? String
    parameters.ad = clickEvent["ad"] as? String

    // Extract custom parameters
    parameters.customParameters = extractCustomParameters(from: clickEvent)

    // Extract routing information
    parameters.deepLinkValue = clickEvent["deep_link_value"] as? String
    parameters.deepLinkSubValue1 = clickEvent["deep_link_sub1"] as? String
    parameters.deepLinkSubValue2 = clickEvent["deep_link_sub2"] as? String
    parameters.deepLinkSubValue3 = clickEvent["deep_link_sub3"] as? String

    return parameters
  }

  private func extractParameters(from url: URL) -> [String: Any] {
    var parameters: [String: Any] = [:]

    // Extract URL components
    if let components = URLComponents(url: url, resolvingAgainstBaseURL: false) {
      // Extract query parameters
      if let queryItems = components.queryItems {
        for item in queryItems {
          parameters[item.name] = item.value
        }
      }

      // Extract path components
      let pathComponents = components.path.components(separatedBy: "/").filter { !$0.isEmpty }
      if !pathComponents.isEmpty {
        parameters["path_components"] = pathComponents
      }
    }

    return parameters
  }

  private func extractCustomParameters(from clickEvent: [String: Any]) -> [String: Any] {
    var customParameters: [String: Any] = [:]

    // Define known AppsFlyer parameters to exclude
    let knownParameters = Set([
      "campaign", "media_source", "channel", "keywords", "adgroup", "adset", "ad",
      "deep_link_value", "deep_link_sub1", "deep_link_sub2", "deep_link_sub3",
      "af_status", "af_message", "is_first_launch", "install_time",
    ])

    for (key, value) in clickEvent {
      if !knownParameters.contains(key) {
        customParameters[key] = value
      }
    }

    return customParameters
  }

  // MARK: - Routing

  private func routeToDestination(parameters: DeepLinkParameters) {
    Logger.info("AppsFlyerDeepLinkHandler: Routing to destination with parameters")

    // Determine routing destination based on deep link value
    guard let deepLinkValue = parameters.deepLinkValue else {
      Logger.warning("AppsFlyerDeepLinkHandler: No deep link value found, using default routing")
      routeToDefault()
      return
    }

    // Route based on deep link value
    switch deepLinkValue.lowercased() {
    case "chat", "conversation":
      routeToChat(parameters: parameters)
    case "design", "create":
      routeToDesign(parameters: parameters)
    case "profile", "user":
      routeToProfile(parameters: parameters)
    case "subscription", "premium", "paywall":
      routeToSubscription(parameters: parameters)
    case "onboarding", "tutorial":
      routeToOnboarding(parameters: parameters)
    default:
      Logger.info(
        "AppsFlyerDeepLinkHandler: Unknown deep link value '\(deepLinkValue)', using default routing"
      )
      routeToDefault()
    }
  }

  private func routeToDestination(from parameters: [String: Any]) {
    // Convert dictionary to DeepLinkParameters for consistency
    let deepLinkParams = DeepLinkParameters()
    deepLinkParams.deepLinkValue = parameters["deep_link_value"] as? String
    deepLinkParams.customParameters = parameters

    routeToDestination(parameters: deepLinkParams)
  }

  // MARK: - Specific Routing Methods

  private func routeToChat(parameters: DeepLinkParameters) {
    Logger.info("AppsFlyerDeepLinkHandler: Routing to chat")

    // Post notification for chat routing
    NotificationCenter.default.post(
      name: .deepLinkRouteToChat,
      object: nil,
      userInfo: ["parameters": parameters]
    )
  }

  private func routeToDesign(parameters: DeepLinkParameters) {
    Logger.info("AppsFlyerDeepLinkHandler: Routing to design")

    // Post notification for design routing
    NotificationCenter.default.post(
      name: .deepLinkRouteToDesign,
      object: nil,
      userInfo: ["parameters": parameters]
    )
  }

  private func routeToProfile(parameters: DeepLinkParameters) {
    Logger.info("AppsFlyerDeepLinkHandler: Routing to profile")

    // Post notification for profile routing
    NotificationCenter.default.post(
      name: .deepLinkRouteToProfile,
      object: nil,
      userInfo: ["parameters": parameters]
    )
  }

  private func routeToSubscription(parameters: DeepLinkParameters) {
    Logger.info("AppsFlyerDeepLinkHandler: Routing to subscription")

    // Post notification for subscription routing
    NotificationCenter.default.post(
      name: .deepLinkRouteToSubscription,
      object: nil,
      userInfo: ["parameters": parameters]
    )
  }

  private func routeToOnboarding(parameters: DeepLinkParameters) {
    Logger.info("AppsFlyerDeepLinkHandler: Routing to onboarding")

    // Post notification for onboarding routing
    NotificationCenter.default.post(
      name: .deepLinkRouteToOnboarding,
      object: nil,
      userInfo: ["parameters": parameters]
    )
  }

  private func routeToDefault() {
    Logger.info("AppsFlyerDeepLinkHandler: Routing to default destination")

    // Post notification for default routing
    NotificationCenter.default.post(
      name: .deepLinkRouteToDefault,
      object: nil,
      userInfo: [:]
    )
  }

  // MARK: - Event Tracking

  private func trackDeepLinkEvent(parameters: [String: Any], status: String) {
    var eventParameters = parameters
    eventParameters["deep_link_status"] = status
    eventParameters["timestamp"] = Date().timeIntervalSince1970

    // Post notification for analytics tracking
    NotificationCenter.default.post(
      name: .deepLinkEventTracked,
      object: nil,
      userInfo: ["parameters": eventParameters]
    )
  }
}

// MARK: - Deep Link Parameters Model

/// Deep link parameters extracted from AppsFlyer
public class DeepLinkParameters: ObservableObject {

  // MARK: - Attribution Parameters

  /// Campaign name
  @Published public var campaign: String?

  /// Media source (e.g., facebook, google)
  @Published public var mediaSource: String?

  /// Channel
  @Published public var channel: String?

  /// Keywords
  @Published public var keywords: String?

  /// Ad group
  @Published public var adGroup: String?

  /// Ad set
  @Published public var adSet: String?

  /// Ad
  @Published public var ad: String?

  // MARK: - Deep Link Values

  /// Main deep link value
  @Published public var deepLinkValue: String?

  /// Deep link sub value 1
  @Published public var deepLinkSubValue1: String?

  /// Deep link sub value 2
  @Published public var deepLinkSubValue2: String?

  /// Deep link sub value 3
  @Published public var deepLinkSubValue3: String?

  // MARK: - Custom Parameters

  /// Custom parameters from the deep link
  @Published public var customParameters: [String: Any] = [:]

  // MARK: - Computed Properties

  /// Check if this is a marketing campaign deep link
  public var isMarketingCampaign: Bool {
    return campaign != nil || mediaSource != nil
  }

  /// Check if this is an organic deep link
  public var isOrganic: Bool {
    return !isMarketingCampaign
  }

  /// Get all parameters as dictionary
  public var allParameters: [String: Any] {
    var parameters: [String: Any] = customParameters

    if let campaign = campaign { parameters["campaign"] = campaign }
    if let mediaSource = mediaSource { parameters["media_source"] = mediaSource }
    if let channel = channel { parameters["channel"] = channel }
    if let keywords = keywords { parameters["keywords"] = keywords }
    if let adGroup = adGroup { parameters["adgroup"] = adGroup }
    if let adSet = adSet { parameters["adset"] = adSet }
    if let ad = ad { parameters["ad"] = ad }
    if let deepLinkValue = deepLinkValue { parameters["deep_link_value"] = deepLinkValue }
    if let deepLinkSubValue1 = deepLinkSubValue1 {
      parameters["deep_link_sub1"] = deepLinkSubValue1
    }
    if let deepLinkSubValue2 = deepLinkSubValue2 {
      parameters["deep_link_sub2"] = deepLinkSubValue2
    }
    if let deepLinkSubValue3 = deepLinkSubValue3 {
      parameters["deep_link_sub3"] = deepLinkSubValue3
    }

    return parameters
  }

  public init() {}
}

// MARK: - Notification Names Extension

extension Notification.Name {

  // Deep link routing notifications
  public static let deepLinkRouteToChat = Notification.Name("DeepLinkRouteToChat")
  public static let deepLinkRouteToDesign = Notification.Name("DeepLinkRouteToDesign")
  public static let deepLinkRouteToProfile = Notification.Name("DeepLinkRouteToProfile")
  public static let deepLinkRouteToSubscription = Notification.Name("DeepLinkRouteToSubscription")
  public static let deepLinkRouteToOnboarding = Notification.Name("DeepLinkRouteToOnboarding")
  public static let deepLinkRouteToDefault = Notification.Name("DeepLinkRouteToDefault")

  // Deep link event tracking
  public static let deepLinkEventTracked = Notification.Name("DeepLinkEventTracked")
}
