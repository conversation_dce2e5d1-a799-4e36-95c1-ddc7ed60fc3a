// AppDependencyContainer.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/18.
//

import FirebaseFirestore
import Foundation
import Moya

// Import AppsFlyer related types
// Note: These will be available once the AppsFlyer files are properly integrated

/// 应用依赖容器
/// 负责创建和管理所有模块的依赖
public final class AppDependencyContainer {
  // MARK: - 单例

  /// 共享实例
  public static let shared = AppDependencyContainer()

  // MARK: - 核心基础设施依赖

  /// 日志服务
  public let loggerService: LoggerService

  /// 错误报告服务
  public let errorReportingService: ErrorReportingService

  /// 配置服务
  public let configService: ConfigService

  /// 存储服务
  public let storageService: StorageService

  /// 网络服务
  public let networkService: NetworkService

  /// 查询管理器（SWR）
  public let queryManager: QueryManager

  /// 用户行为分析服务 - Firebase Analytics
  public let behaviorAnalyticsService: AnalyticsService

  /// 营销归因分析服务 - AppsFlyer
  public let attributionAnalyticsService: AnalyticsService

  /// 深度链接处理器 (TODO: 重构为 DeepLinkService)
  public let deepLinkHandler: AppsFlyerDeepLinkHandler

  /// 兼容性：保持原有的 analyticsService 接口（指向 behaviorAnalyticsService）
  public var analyticsService: AnalyticsService {
    return behaviorAnalyticsService
  }

  // MARK: - 认证服务

  /// 认证服务
  public let authService: AuthService

  // MARK: - 仓储

  /// 用户仓储
  public let userRepository: UserRepository

  /// 消息仓储
  public let messageRepository: MessageRepository

  /// 聊天仓储
  public let chatRepository: ChatRepository

  /// 图片任务仓储
  public let imageTaskRepository: ImageTaskRepository

  /// 资产仓储
  public let assetRepository: AssetRepository

  // MARK: - 共享状态

  /// 用户状态管理器
  private let userStateManager: UserStateManager

  // MARK: - 服务

  /// API服务
  public let apiService: APIService

  /// 视频效果服务
  public let videoEffectService: VideoEffectService

  // MARK: - 模块依赖

  /// 模块依赖
  private lazy var moduleDependencies: ModuleDependencies = {
    return ModuleDependencies(
      loggerService: loggerService,
      errorReportingService: errorReportingService,
      configService: configService,
      analyticsService: behaviorAnalyticsService,  // 使用行为分析服务作为默认
      authService: authService,
      userRepository: userRepository,
      chatRepository: chatRepository,
      messageRepository: messageRepository,
      imageTaskRepository: imageTaskRepository,
      userStateManager: userStateManager,
      storageService: storageService,
      apiService: apiService,
      networkService: networkService,
      videoEffectService: videoEffectService
    )
  }()

  // MARK: - 功能模块

  /// 认证模块
  public lazy var authModule: AuthModule = {
    return AuthModule(dependencies: moduleDependencies)
  }()

  /// 用户模块
  public lazy var userModule: UserModule = {
    return UserModule(dependencies: moduleDependencies)
  }()

  /// 聊天模块
  public lazy var chatModule: ChatModule = {
    return ChatModule(dependencies: moduleDependencies)
  }()

  /// 存储模块
  public lazy var storageModule: StorageModule = {
    return StorageModule(
      storageService: storageService, apiService: apiService, assetService: assetModule.assetService
    )
  }()

  /// 设计模块
  public lazy var designModule: DesignModule = {
    return DesignModule(dependencies: moduleDependencies)
  }()

  /// 视频模块
  public lazy var videoModule: VideoModule = {
    return VideoModule(dependencies: moduleDependencies)
  }()

  /// 订阅模块
  public lazy var subscriptionModule: SubscriptionModule = {
    let subscriptionDependencies = SubscriptionModuleDependencies(
      configService: configService,
      userService: userModule.userService,
      analyticsService: behaviorAnalyticsService  // 订阅使用行为分析
    )
    return SubscriptionModule(dependencies: subscriptionDependencies)
  }()

  /// 资产模块
  public lazy var assetModule: AssetModule = {
    let assetDependencies = AssetModuleDependencies(
      assetRepository: assetRepository,
      apiService: apiService,
      userStateManager: userStateManager,
      uploadConfiguration: .default,
      loggerService: loggerService,
      analyticsService: behaviorAnalyticsService  // 资产模块使用行为分析
    )
    return AssetModule(dependencies: assetDependencies)
  }()

  /// 媒体模块
  public lazy var mediaModule: MediaModule = {
    return MediaModule(dependencies: moduleDependencies)
  }()

  /// 支持模块
  public lazy var supportModule: SupportModule = {
    return SupportModule(dependencies: moduleDependencies)
  }()

  // MARK: - Initialization

  private init() {
    // 创建基础设施服务
    self.loggerService = DefaultLoggerAdapter()
    self.errorReportingService = SentryAdapter(
      dsn:
        "https://<EMAIL>/4508976028581888"
    )
    self.configService = FirebaseConfigAdapter()
    self.storageService = FirebaseStorageAdapter()
    self.authService = FirebaseAuthAdapter()
    self.networkService = DefaultNetworkService()

    // 创建分析服务 - 职责分离架构
    self.behaviorAnalyticsService = FirebaseAnalyticsAdapter()
    self.attributionAnalyticsService = AppsFlyerAnalyticsAdapter()

    // 创建深度链接处理器 (TODO: 重构为 DeepLinkService)
    self.deepLinkHandler = AppsFlyerDeepLinkHandler()

    // 创建查询管理器（需要在主线程上创建）
    self.queryManager = QueryManager.shared  // 临时使用 CacheManager，稍后修复

    // 创建仓储
    let repositoryFactory = FirebaseRepositoryFactory.shared
    self.userRepository = repositoryFactory.makeUserRepository()
    self.chatRepository = repositoryFactory.makeChatRepository()
    self.messageRepository = repositoryFactory.makeMessageRepository()
    self.imageTaskRepository = FirestoreImageTaskRepository()
    self.assetRepository = repositoryFactory.makeAssetRepository()

    // 创建共享状态
    self.userStateManager = UserStateManager.shared

    // 创建服务
    // 创建Firebase token提供者
    let tokenProvider = FirebaseTokenProvider(authService: self.authService)

    // 创建AccessTokenPlugin
    let accessTokenPlugin = AccessTokenPlugin { _ in
      return tokenProvider.getToken()
    }

    // 创建带认证插件的MoyaProvider
    let provider = MoyaProvider<APIEndpoint>(plugins: [accessTokenPlugin])

    self.apiService = DefaultAPIService(provider: provider)

    // 创建视频效果服务
    self.videoEffectService = DefaultVideoEffectService(apiService: self.apiService)

    // Initialization服务
    setupServices()
  }

  private func setupServices() {
    // Initialization日志服务
    Logger.service = loggerService

    // Initialization错误报告服务
    //        errorReportingService.setup()
  }

  // MARK: - AppsFlyer Configuration

  /// Configure AppsFlyer with specific configuration
  /// - Parameter config: AppsFlyer configuration
  public func configureAppsFlyer(with config: AppsFlyerConfiguration) async throws {
    guard let appsflyerAdapter = attributionAnalyticsService as? AppsFlyerAnalyticsAdapter else {
      throw AppsFlyerConfigurationError.invalidDevKey("AppsFlyer adapter not available")
    }

    // Inject deep link handler as delegate
    try await appsflyerAdapter.configure(with: config, deepLinkService: deepLinkHandler)
    Logger.info("AppDependencyContainer: AppsFlyer configured successfully with deep link service")
  }

  /// Configure AppsFlyer with remote config values
  public func configureAppsFlyerFromRemoteConfig() async throws {
    do {
      // Fetch AppsFlyer configuration from Firebase Remote Config
      let devKey = try configService.getString(for: "appsflyer_dev_key")
      let appleAppID = try configService.getString(for: "appsflyer_apple_app_id")
      let isDebugEnabled = try configService.getBool(for: "appsflyer_debug_enabled")

      let config = AppsFlyerConfiguration(
        devKey: devKey,
        appleAppID: appleAppID,
        isDebugEnabled: isDebugEnabled
      )

      try await configureAppsFlyer(with: config)
      Logger.info(
        "AppDependencyContainer: AppsFlyer configured from remote config with dependency injection")

    } catch {
      Logger.error(
        "AppDependencyContainer: Failed to configure AppsFlyer from remote config - \(error)")

      // Fallback to default configuration if remote config fails
      let fallbackConfig = AppsFlyerConfiguration.development(
        devKey: "YOUR_DEV_KEY_HERE",  // Replace with actual dev key
        appleAppID: "YOUR_APPLE_APP_ID_HERE"  // Replace with actual Apple App ID
      )

      try await configureAppsFlyer(with: fallbackConfig)
      Logger.warning("AppDependencyContainer: Using fallback AppsFlyer configuration")
    }
  }
}
