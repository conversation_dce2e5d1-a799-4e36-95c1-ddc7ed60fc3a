//
//  AppsFlyerDeepLinkService.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/21.
//

import AppTrackingTransparency
import AppsFlyerLib
import Combine
import Foundation
import GoogleSignIn

/// AppsFlyer implementation of DeepLinkService
/// Handles AppsFlyer deep links, URL routing, and SDK lifecycle
public final class AppsFlyerDeepLinkService: NSObject, ObservableObject, DeepLinkService,
  DeepLinkDelegate
{

  // MARK: - Published Properties

  /// Deep link data received from AppsFlyer
  @Published public private(set) var deepLinkData: [String: Any]?

  /// Indicates if a deep link is being processed
  @Published public private(set) var isProcessingDeepLink = false

  // MARK: - Private Properties

  private var configuration: DeepLinkConfiguration?
  private let logger = Logger.self

  // MARK: - DeepLinkService Implementation

  public var isConfigured: Bool {
    return configuration != nil
  }

  public func configure(with configuration: DeepLinkConfiguration) async throws {
    self.configuration = configuration
    logger.info("AppsFlyerDeepLinkService: Configured with enabled: \(configuration.enabled)")
  }

  // MARK: - URL Handling

  public func handleURL(_ url: URL) -> Bool {
    logger.info("AppsFlyerDeepLinkService: Processing URL Scheme - \(url.absoluteString)")

    // Handle Google Sign-In URL first
    if GIDSignIn.sharedInstance.handle(url) {
      logger.info("AppsFlyerDeepLinkService: Google Sign-In URL handled")
      return true
    }

    // Handle AppsFlyer deep links
    processDeepLink(from: url)
    logger.info("AppsFlyerDeepLinkService: URL Scheme processed successfully")
    return true
  }

  public func handleUniversalLink(_ userActivity: NSUserActivity) -> Bool {
    guard userActivity.activityType == NSUserActivityTypeBrowsingWeb,
      let url = userActivity.webpageURL
    else {
      logger.warning("AppsFlyerDeepLinkService: Invalid user activity for Universal Link")
      return false
    }

    logger.info("AppsFlyerDeepLinkService: Processing Universal Link - \(url.absoluteString)")

    // Notify AppsFlyer SDK
    AppsFlyerLib.shared().continue(userActivity, restorationHandler: nil)

    // Handle deep link routing
    processDeepLink(from: url)

    logger.info("AppsFlyerDeepLinkService: Universal Link processed successfully")
    return true
  }

  // MARK: - Lifecycle Management

  public func handleAppBecameActive() {
    logger.info("🚀 AppsFlyerDeepLinkService: Starting SDK")
    AppsFlyerLib.shared().start()

    // Request ATT authorization on iOS 14+
    if #available(iOS 14, *) {
      requestATTPermission()
    }
  }

  public func handleAppMovedToBackground() {
    logger.info("AppsFlyerDeepLinkService: App moved to background")
    // AppsFlyer handles background state automatically
  }

  // MARK: - DeepLinkDelegate Implementation

  public func didResolveDeepLink(_ result: DeepLinkResult) {
    logger.info("AppsFlyerDeepLinkService: UDL Deep link resolved with status: \(result.status)")

    // Process the deep link result directly
    processDeepLink(result)

    // Also post notification for backward compatibility
    NotificationCenter.default.post(
      name: .appsFlyerDeepLinkReceived,
      object: nil,
      userInfo: ["deepLinkResult": result]
    )
  }

  // MARK: - Deep Link Processing

  /// Process deep link result from AppsFlyer
  /// - Parameter result: AppsFlyer deep link result
  public func processDeepLink(_ result: DeepLinkResult) {
    isProcessingDeepLink = true

    switch result.status {
    case .found:
      handleDeepLinkFound(result)
    case .notFound:
      handleDeepLinkNotFound()
    case .failure:
      handleDeepLinkFailure(result.error)
    @unknown default:
      handleUnknownDeepLinkStatus()
    }

    isProcessingDeepLink = false
  }

  /// Process deep link from URL (for manual deep link handling)
  /// - Parameter url: Deep link URL
  public func processDeepLink(from url: URL) {
    logger.info("AppsFlyerDeepLinkService: Processing manual deep link - \(url)")

    let parameters = extractParameters(from: url)
    deepLinkData = parameters

    routeToDestination(from: parameters)
  }

  // MARK: - Private ATT Handling

  @available(iOS 14, *)
  private func requestATTPermission() {
    ATTrackingManager.requestTrackingAuthorization { status in
      DispatchQueue.main.async {
        switch status {
        case .authorized:
          self.logger.info("AppsFlyerDeepLinkService: ATT permission granted")
        case .denied:
          self.logger.info("AppsFlyerDeepLinkService: ATT permission denied")
        case .restricted:
          self.logger.info("AppsFlyerDeepLinkService: ATT permission restricted")
        case .notDetermined:
          self.logger.info("AppsFlyerDeepLinkService: ATT permission not determined")
        @unknown default:
          self.logger.warning("AppsFlyerDeepLinkService: Unknown ATT status")
        }
      }
    }
  }

  // MARK: - Private Deep Link Handlers

  private func handleDeepLinkFound(_ result: DeepLinkResult) {
    guard let deepLink = result.deepLink else {
      logger.warning("AppsFlyerDeepLinkService: Deep link found but no data available")
      return
    }

    logger.info(
      "AppsFlyerDeepLinkService: UDL Deep link found - isDeferred: \(deepLink.isDeferred)")

    let clickEvent = deepLink.clickEvent
    deepLinkData = clickEvent

    // Extract routing information
    let parameters = extractDeepLinkParameters(from: clickEvent)

    // If UDL has deeplinkValue, prioritize it over clickEvent deep_link_value
    if let deeplinkValue = deepLink.deeplinkValue {
      parameters.deepLinkValue = deeplinkValue
      logger.info("AppsFlyerDeepLinkService: Using UDL deeplinkValue: \(deeplinkValue)")
    }

    // Route to appropriate destination
    routeToDestination(parameters: parameters)

    // Track deep link event with UDL status
    let status = deepLink.isDeferred ? "udl_deferred" : "udl_found"
    trackDeepLinkEvent(parameters: parameters.allParameters, status: status)
  }

  private func handleDeepLinkNotFound() {
    logger.info("AppsFlyerDeepLinkService: Deep link not found")
    deepLinkData = nil

    // Track deep link event
    trackDeepLinkEvent(parameters: [:], status: "not_found")
  }

  private func handleDeepLinkFailure(_ error: Error?) {
    let errorMessage = error?.localizedDescription ?? "Unknown error"
    logger.error("AppsFlyerDeepLinkService: Deep link processing failed - \(errorMessage)")

    deepLinkData = nil

    // Track deep link event
    trackDeepLinkEvent(parameters: ["error": errorMessage], status: "failure")
  }

  private func handleUnknownDeepLinkStatus() {
    logger.warning("AppsFlyerDeepLinkService: Unknown deep link status")
    deepLinkData = nil

    // Track deep link event
    trackDeepLinkEvent(parameters: [:], status: "unknown")
  }

  // MARK: - Parameter Extraction

  private func extractDeepLinkParameters(from clickEvent: [String: Any]) -> DeepLinkParameters {
    let parameters = DeepLinkParameters()

    // Extract common parameters
    parameters.campaign = clickEvent["campaign"] as? String
    parameters.mediaSource = clickEvent["media_source"] as? String
    parameters.channel = clickEvent["channel"] as? String
    parameters.keywords = clickEvent["keywords"] as? String
    parameters.adGroup = clickEvent["adgroup"] as? String
    parameters.adSet = clickEvent["adset"] as? String
    parameters.ad = clickEvent["ad"] as? String

    // Extract custom parameters
    parameters.customParameters = extractCustomParameters(from: clickEvent)

    // Extract routing information
    parameters.deepLinkValue = clickEvent["deep_link_value"] as? String
    parameters.deepLinkSubValue1 = clickEvent["deep_link_sub1"] as? String
    parameters.deepLinkSubValue2 = clickEvent["deep_link_sub2"] as? String
    parameters.deepLinkSubValue3 = clickEvent["deep_link_sub3"] as? String

    return parameters
  }

  private func extractParameters(from url: URL) -> [String: Any] {
    var parameters: [String: Any] = [:]

    // Extract URL components
    if let components = URLComponents(url: url, resolvingAgainstBaseURL: false) {
      // Extract query parameters
      if let queryItems = components.queryItems {
        for item in queryItems {
          parameters[item.name] = item.value
        }
      }

      // Extract path components
      let pathComponents = components.path.components(separatedBy: "/").filter { !$0.isEmpty }
      if !pathComponents.isEmpty {
        parameters["path_components"] = pathComponents
      }
    }

    return parameters
  }

  private func extractCustomParameters(from clickEvent: [String: Any]) -> [String: Any] {
    var customParameters: [String: Any] = [:]

    // Define known AppsFlyer parameters to exclude
    let knownParameters: Set<String> = [
      "campaign", "media_source", "channel", "keywords", "adgroup", "adset", "ad",
      "deep_link_value", "deep_link_sub1", "deep_link_sub2", "deep_link_sub3",
      "af_status", "af_message", "is_first_launch", "install_time",
    ]

    // Extract custom parameters (anything not in known parameters)
    for (key, value) in clickEvent {
      if !knownParameters.contains(key) {
        customParameters[key] = value
      }
    }

    return customParameters
  }

  // MARK: - Routing

  private func routeToDestination(parameters: DeepLinkParameters) {
    logger.info("AppsFlyerDeepLinkService: Routing to destination with parameters")

    // Determine routing destination based on deep link value
    guard let deepLinkValue = parameters.deepLinkValue else {
      logger.warning("AppsFlyerDeepLinkService: No deep link value found, using default routing")
      routeToDefault()
      return
    }

    // Route based on deep link value
    switch deepLinkValue.lowercased() {
    case "chat", "conversation":
      routeToChat(parameters: parameters)
    case "design", "create":
      routeToCreate(parameters: parameters)
    case "explore":
      routeToExplore(parameters: parameters)
    case "profile", "user":
      routeToProfile(parameters: parameters)
    case "subscription", "premium", "paywall":
      routeToSubscription(parameters: parameters)
    case "template":
      routeToTemplate(parameters: parameters)
    case "onboarding", "tutorial":
      routeToOnboarding(parameters: parameters)
    default:
      logger.info(
        "AppsFlyerDeepLinkService: Unknown deep link value '\(deepLinkValue)', using default routing"
      )
      routeToDefault()
    }
  }

  private func routeToDestination(from parameters: [String: Any]) {
    // Convert dictionary to DeepLinkParameters for consistency
    let deepLinkParams = DeepLinkParameters()
    deepLinkParams.deepLinkValue = parameters["deep_link_value"] as? String
    deepLinkParams.customParameters = parameters

    routeToDestination(parameters: deepLinkParams)
  }

  // MARK: - Specific Routing Methods

  private func routeToChat(parameters: DeepLinkParameters) {
    logger.info("AppsFlyerDeepLinkService: Routing to chat")

    // Post notification for chat routing
    NotificationCenter.default.post(
      name: .deepLinkRouteToChat,
      object: nil,
      userInfo: ["parameters": parameters]
    )
  }

  private func routeToCreate(parameters: DeepLinkParameters) {
    logger.info("AppsFlyerDeepLinkService: Routing to create")

    // Post notification for create routing
    NotificationCenter.default.post(
      name: .deepLinkRouteToCreate,
      object: nil,
      userInfo: ["parameters": parameters]
    )
  }

  private func routeToExplore(parameters: DeepLinkParameters) {
    logger.info("AppsFlyerDeepLinkService: Routing to explore")

    // Post notification for explore routing
    NotificationCenter.default.post(
      name: .deepLinkRouteToExplore,
      object: nil,
      userInfo: ["parameters": parameters]
    )
  }

  private func routeToProfile(parameters: DeepLinkParameters) {
    logger.info("AppsFlyerDeepLinkService: Routing to profile")

    // Post notification for profile routing
    NotificationCenter.default.post(
      name: .deepLinkRouteToProfile,
      object: nil,
      userInfo: ["parameters": parameters]
    )
  }

  private func routeToSubscription(parameters: DeepLinkParameters) {
    logger.info("AppsFlyerDeepLinkService: Routing to subscription")

    // Post notification for subscription routing
    NotificationCenter.default.post(
      name: .deepLinkRouteToSubscription,
      object: nil,
      userInfo: ["parameters": parameters]
    )
  }

  private func routeToOnboarding(parameters: DeepLinkParameters) {
    logger.info("AppsFlyerDeepLinkService: Routing to onboarding")

    // Post notification for onboarding routing
    NotificationCenter.default.post(
      name: .deepLinkRouteToOnboarding,
      object: nil,
      userInfo: ["parameters": parameters]
    )
  }

  private func routeToTemplate(parameters: DeepLinkParameters) {
    logger.info("AppsFlyerDeepLinkService: Routing to template")

    // Post notification for template routing
    NotificationCenter.default.post(
      name: .deepLinkRouteToTemplate,
      object: nil,
      userInfo: ["parameters": parameters]
    )
  }

  private func routeToDefault() {
    logger.info("AppsFlyerDeepLinkService: Routing to default destination")

    // Post notification for default routing
    NotificationCenter.default.post(
      name: .deepLinkRouteToDefault,
      object: nil,
      userInfo: [:]
    )
  }

  // MARK: - Event Tracking

  private func trackDeepLinkEvent(parameters: [String: Any], status: String) {
    var eventParameters = parameters
    eventParameters["deep_link_status"] = status
    eventParameters["timestamp"] = Date().timeIntervalSince1970

    // Post notification for event tracking
    NotificationCenter.default.post(
      name: .deepLinkEventTracked,
      object: nil,
      userInfo: ["parameters": eventParameters]
    )

    logger.debug("AppsFlyerDeepLinkService: Deep link event tracked - status: \(status)")
  }
}
