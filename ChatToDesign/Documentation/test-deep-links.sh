#!/bin/bash

# AppsFlyer OneLink Deep Link Testing Script
# This script helps test deep link functionality using URL schemes

echo "🔗 AppsFlyer OneLink Deep Link Testing"
echo "======================================"

# Check if simulator is running
if ! xcrun simctl list devices | grep -q "Booted"; then
    echo "❌ No iOS Simulator is currently running"
    echo "Please start an iOS Simulator first"
    exit 1
fi

echo "✅ iOS Simulator detected"

# Test URL Scheme Deep Links
echo ""
echo "📱 Testing URL Scheme Deep Links..."

# Function to test a deep link
test_deep_link() {
    local url="$1"
    local description="$2"
    
    echo ""
    echo "🧪 Testing: $description"
    echo "URL: $url"
    
    # Open URL in simulator
    xcrun simctl openurl booted "$url"
    
    echo "✅ Deep link sent to simulator"
    echo "Check the app to verify routing..."
    
    # Wait for user confirmation
    read -p "Press Enter to continue to next test..."
}

# Test basic routing
test_deep_link "chattodesign://create?deep_link_value=create" "Route to Create Page"
test_deep_link "chattodesign://explore?deep_link_value=explore" "Route to Explore Page"
test_deep_link "chattodesign://profile?deep_link_value=profile" "Route to Profile Page"

# Test with parameters
test_deep_link "chattodesign://template?deep_link_value=template&deep_link_sub1=video_template_001" "Route to Template with ID"
test_deep_link "chattodesign://subscription?deep_link_value=subscription&campaign=test_campaign" "Route to Subscription with Campaign"

# Test marketing parameters
test_deep_link "chattodesign://create?deep_link_value=create&pid=email&c=newsletter&deep_link_sub2=email_campaign" "Marketing Campaign Test"

echo ""
echo "🎉 Deep Link Testing Complete!"
echo ""
echo "📋 Verification Checklist:"
echo "- [ ] App opened for each test"
echo "- [ ] Correct tab was selected"
echo "- [ ] Parameters were logged correctly"
echo "- [ ] No errors in Xcode console"
echo ""
echo "📝 Next Steps:"
echo "1. Configure AppsFlyer OneLink in the console"
echo "2. Update AASA file with correct app information"
echo "3. Test Universal Links with real URLs"
echo "4. Test on physical device for production validation"
