# AppsFlyer Integration Refactor

## Overview
This document outlines the refactoring of Apps<PERSON><PERSON>er integration to consolidate all AppsFlyer-related functionality within the `AppsFlyerDeepLinkHandler` and keep the main app files clean.

## Changes Made

### 1. AppsFlyerDeepLinkHandler Enhancements

**File**: `ChatToDesign/Infrastructure/ThirdParty/AppsFlyer/AppsFlyerDeepLinkHandler.swift`

#### New Functionality Added:
- **SDK Lifecycle Management**: Added `handleAppBecameActive()` method to start AppsFlyer SDK
- **URL Handling**: Added `handleURL(_:)` method to process URL schemes (including Google Sign-In)
- **Universal Link Handling**: Added `handleUniversalLink(_:)` method to process universal links
- **ATT Integration**: Added complete App Tracking Transparency permission handling

#### Key Methods:
```swift
// SDK lifecycle
public func handleAppBecameActive()

// URL handling
public func handleURL(_ url: URL) -> Bool
public func handleUniversalLink(_ userActivity: NSUserActivity) -> Bool

// ATT permission handling
@available(iOS 14, *)
private func requestATTPermission()
private func attStatusString(_ status: ATTrackingManager.AuthorizationStatus) -> String
```

### 2. ChatToDesignApp.swift Simplification

**File**: `ChatToDesign/Application/ChatToDesignApp.swift`

#### Changes:
- **Removed imports**: Removed `AppsFlyerLib` and `GoogleSignIn` imports
- **Simplified URL handling**: Delegated all URL/Universal Link handling to `AppsFlyerDeepLinkHandler`
- **Added scenePhase monitoring**: Integrated AppsFlyer SDK lifecycle with SwiftUI's `scenePhase`
- **Removed redundant methods**: Eliminated `handleURL`, `handleUniversalLink`, and `handleDeepLinkError` methods

#### New Implementation:
```swift
.onOpenURL { url in
  let container = AppDependencyContainer.shared
  _ = container.deepLinkHandler.handleURL(url)
}
.onContinueUserActivity(NSUserActivityTypeBrowsingWeb) { userActivity in
  let container = AppDependencyContainer.shared
  _ = container.deepLinkHandler.handleUniversalLink(userActivity)
}
.onChange(of: scenePhase) { newPhase in
  switch newPhase {
  case .active:
    let container = AppDependencyContainer.shared
    container.deepLinkHandler.handleAppBecameActive()
  // ... other cases
  }
}
```

### 3. AppDelegate Cleanup

**File**: `ChatToDesign/Application/AppDelegate.swift`

#### Changes:
- **Removed sendLaunch method**: Eliminated `@objc func sendLaunch()` and related notification observer
- **Removed AppsFlyer Integration extension**: Moved all AppsFlyer-related code to `AppsFlyerDeepLinkHandler`
- **Simplified URL handling**: Kept compatibility method but delegated to handler
- **Removed ATT handling**: Moved to `AppsFlyerDeepLinkHandler`

#### Remaining URL Method:
```swift
func application(
  _ app: UIApplication,
  open url: URL,
  options: [UIApplication.OpenURLOptionsKey: Any] = [:]
) -> Bool {
  let container = AppDependencyContainer.shared
  return container.deepLinkHandler.handleURL(url)
}
```

## Benefits

### 1. **Single Responsibility Principle**
- `AppsFlyerDeepLinkHandler` now handles ALL AppsFlyer-related functionality
- `ChatToDesignApp.swift` focuses on app structure and SwiftUI lifecycle
- `AppDelegate.swift` focuses on system-level app lifecycle events

### 2. **Reduced Code Duplication**
- Eliminated duplicate URL handling between `AppDelegate` and `ChatToDesignApp`
- Consolidated AppsFlyer SDK lifecycle management
- Single source of truth for deep link processing

### 3. **Improved Maintainability**
- All AppsFlyer changes now happen in one place
- Cleaner separation of concerns
- Easier to test and debug AppsFlyer functionality

### 4. **SwiftUI Best Practices**
- Proper use of `scenePhase` for lifecycle management
- Removed unnecessary UIKit dependencies from SwiftUI app
- Better integration with SwiftUI app lifecycle

## Migration Notes

### For Developers:
1. **AppsFlyer SDK lifecycle** is now managed through SwiftUI's `scenePhase` monitoring
2. **URL handling** is centralized in `AppsFlyerDeepLinkHandler`
3. **ATT permissions** are handled automatically when the app becomes active
4. **Deep link routing** continues to work through the same notification system

### Backward Compatibility:
- All existing deep link notifications continue to work
- URL handling from `AppDelegate` still functions (delegates to handler)
- No breaking changes to the public API

## Testing Recommendations

1. **Test URL schemes**: Verify Google Sign-In and custom deep links work
2. **Test Universal Links**: Ensure web-based deep links route correctly
3. **Test App lifecycle**: Confirm AppsFlyer starts when app becomes active
4. **Test ATT permissions**: Verify tracking authorization requests work
5. **Test deep link routing**: Ensure all notification-based routing continues to function

## Future Improvements

1. **Error handling**: Consider adding more robust error handling for URL processing
2. **Analytics**: Add more detailed analytics for deep link success/failure rates
3. **Testing**: Add unit tests for the consolidated handler
4. **Documentation**: Update API documentation for the new handler methods
