# AppsFlyer 架构重构方案

## 问题分析

### 当前架构问题

1. **职责混乱**: `AppsFlyerDeepLinkHandler` 和 `AppsFlyerAnalyticsAdapter` 之间存在循环依赖
2. **违反单一职责原则**: `AppsFlyerAnalyticsAdapter` 既负责分析事件，又要配置深度链接代理
3. **依赖注入不清晰**: 在 `AppsFlyerAnalyticsAdapter` 中通过 `AppDependencyContainer.shared` 访问 `deepLinkHandler`
4. **架构不一致**: 深度链接处理器不遵循项目的 `AnalyticsService` 协议架构

### 当前依赖关系

```
AppDependencyContainer
├── attributionAnalyticsService: AppsFlyerAnalyticsAdapter
└── deepLinkHandler: AppsFlyerDeepLinkHandler

AppsFlyerAnalyticsAdapter
├── 实现 AnalyticsService 协议
├── 配置 AppsFlyer SDK
└── 设置 deepLinkDelegate = container.deepLinkHandler  ❌ 循环依赖

AppsFlyerDeepLinkHandler
├── 实现 DeepLinkDelegate 协议
├── 处理深度链接路由
├── 管理 SDK 生命周期
└── 处理 URL schemes 和 Universal Links
```

## 重构方案

### 方案一：完全隔离 + 协议抽象（推荐）

#### 1. 创建深度链接服务协议

```swift
// ChatToDesign/Infrastructure/Services/DeepLink/DeepLinkService.swift
public protocol DeepLinkService {
    // 基础深度链接处理
    func handleURL(_ url: URL) -> Bool
    func handleUniversalLink(_ userActivity: NSUserActivity) -> Bool
    
    // 生命周期管理
    func handleAppBecameActive()
    
    // 状态监控
    var isProcessingDeepLink: Bool { get }
    var deepLinkData: [String: Any]? { get }
}
```

#### 2. 重构 AppsFlyerDeepLinkHandler

```swift
// ChatToDesign/Infrastructure/Services/DeepLink/AppsFlyerDeepLinkService.swift
public final class AppsFlyerDeepLinkService: NSObject, ObservableObject, DeepLinkService, DeepLinkDelegate {
    // 移除对 AppDependencyContainer 的依赖
    // 专注于深度链接处理逻辑
}
```

#### 3. 简化 AppsFlyerAnalyticsAdapter

```swift
// ChatToDesign/Infrastructure/ThirdParty/AppsFlyer/AppsFlyerAnalyticsAdapter.swift
public final class AppsFlyerAnalyticsAdapter: NSObject, AnalyticsService {
    
    private weak var deepLinkService: (DeepLinkDelegate & DeepLinkService)?
    
    public func configure(with config: AppsFlyerConfiguration, deepLinkService: (DeepLinkDelegate & DeepLinkService)?) async throws {
        // 通过依赖注入接收深度链接服务
        self.deepLinkService = deepLinkService
        
        // 配置 SDK
        if config.deepLinkingEnabled {
            AppsFlyerLib.shared().deepLinkDelegate = deepLinkService
        }
    }
}
```

#### 4. 更新 AppDependencyContainer

```swift
public final class AppDependencyContainer {
    // 深度链接服务
    public let deepLinkService: DeepLinkService
    
    // 营销归因分析服务
    public let attributionAnalyticsService: AnalyticsService
    
    private init() {
        // 先创建深度链接服务
        let appsFlyerDeepLinkService = AppsFlyerDeepLinkService()
        self.deepLinkService = appsFlyerDeepLinkService
        
        // 创建分析服务并注入深度链接服务
        let appsFlyerAdapter = AppsFlyerAnalyticsAdapter()
        self.attributionAnalyticsService = appsFlyerAdapter
        
        // 配置时注入依赖
        // 在 configureAppsFlyer 方法中传递 deepLinkService
    }
}
```

### 方案二：事件驱动架构

#### 1. 创建深度链接事件总线

```swift
// ChatToDesign/Infrastructure/Services/DeepLink/DeepLinkEventBus.swift
public final class DeepLinkEventBus {
    public static let shared = DeepLinkEventBus()
    
    public func publish(_ event: DeepLinkEvent) {
        NotificationCenter.default.post(name: event.notificationName, object: event)
    }
    
    public func subscribe<T: DeepLinkEventHandler>(_ handler: T, to eventType: DeepLinkEvent.Type) {
        // 订阅特定类型的深度链接事件
    }
}
```

#### 2. 独立的深度链接处理器

```swift
// ChatToDesign/Infrastructure/Services/DeepLink/DeepLinkRouter.swift
public final class DeepLinkRouter: NSObject, DeepLinkDelegate {
    private let eventBus = DeepLinkEventBus.shared
    
    public func didResolveDeepLink(_ result: DeepLinkResult) {
        let event = DeepLinkResolvedEvent(result: result)
        eventBus.publish(event)
    }
}
```

#### 3. AppsFlyer 适配器只负责 SDK 配置

```swift
public final class AppsFlyerAnalyticsAdapter: NSObject, AnalyticsService {
    private let deepLinkRouter = DeepLinkRouter()
    
    public func configure(with config: AppsFlyerConfiguration) async throws {
        // 只负责 SDK 配置
        if config.deepLinkingEnabled {
            AppsFlyerLib.shared().deepLinkDelegate = deepLinkRouter
        }
    }
}
```

### 方案三：工厂模式 + 依赖注入

#### 1. 创建 AppsFlyer 服务工厂

```swift
// ChatToDesign/Infrastructure/ThirdParty/AppsFlyer/AppsFlyerServiceFactory.swift
public final class AppsFlyerServiceFactory {
    
    public static func createServices() -> (analyticsService: AnalyticsService, deepLinkService: DeepLinkService) {
        let deepLinkService = AppsFlyerDeepLinkService()
        let analyticsService = AppsFlyerAnalyticsAdapter(deepLinkService: deepLinkService)
        
        return (analyticsService, deepLinkService)
    }
}
```

#### 2. 在 AppDependencyContainer 中使用工厂

```swift
public final class AppDependencyContainer {
    public let attributionAnalyticsService: AnalyticsService
    public let deepLinkService: DeepLinkService
    
    private init() {
        let appsFlyerServices = AppsFlyerServiceFactory.createServices()
        self.attributionAnalyticsService = appsFlyerServices.analyticsService
        self.deepLinkService = appsFlyerServices.deepLinkService
    }
}
```

## 推荐方案详细设计

### 选择方案一：完全隔离 + 协议抽象

#### 优势
1. **清晰的职责分离**: 分析服务只负责分析，深度链接服务只负责路由
2. **遵循 SOLID 原则**: 单一职责、依赖倒置
3. **易于测试**: 可以独立测试每个组件
4. **扩展性强**: 可以轻松替换深度链接实现
5. **符合项目架构**: 遵循现有的协议抽象模式

#### 实施步骤

1. **创建 DeepLinkService 协议**
   - 定义深度链接服务的标准接口
   - 包含 URL 处理、生命周期管理等方法

2. **重构 AppsFlyerDeepLinkHandler**
   - 重命名为 `AppsFlyerDeepLinkService`
   - 实现 `DeepLinkService` 协议
   - 移除对 `AppDependencyContainer` 的直接依赖

3. **简化 AppsFlyerAnalyticsAdapter**
   - 移除深度链接相关逻辑
   - 通过构造函数或配置方法接收深度链接服务
   - 专注于分析事件处理

4. **更新 AppDependencyContainer**
   - 先创建深度链接服务
   - 将深度链接服务注入到分析适配器
   - 保持清晰的依赖关系

5. **更新应用入口点**
   - 更新 `ChatToDesignApp.swift` 使用新的服务接口
   - 更新 `AppDelegate.swift` 的 URL 处理

#### 文件结构

```
ChatToDesign/Infrastructure/
├── Services/
│   └── DeepLink/
│       ├── DeepLinkService.swift           // 协议定义
│       ├── AppsFlyerDeepLinkService.swift  // AppsFlyer 实现
│       └── DeepLinkModels.swift           // 数据模型
└── ThirdParty/
    └── AppsFlyer/
        ├── AppsFlyerAnalyticsAdapter.swift // 纯分析服务
        ├── AppsFlyerConfiguration.swift
        └── AppsFlyerEventMapper.swift
```

## 迁移策略

### 阶段 1: 创建新架构（不破坏现有功能）
- 创建 `DeepLinkService` 协议
- 创建 `AppsFlyerDeepLinkService` 实现
- 保持现有代码不变

### 阶段 2: 逐步迁移
- 更新 `AppDependencyContainer` 使用新服务
- 更新应用入口点
- 测试深度链接功能

### 阶段 3: 清理旧代码
- 移除 `AppsFlyerDeepLinkHandler`
- 简化 `AppsFlyerAnalyticsAdapter`
- 更新文档和测试

## 风险评估

### 低风险
- 协议抽象不会影响现有功能
- 可以逐步迁移，保持向后兼容

### 中等风险
- 需要更新多个文件的依赖关系
- 需要仔细测试深度链接功能

### 缓解措施
- 保持现有接口不变，直到迁移完成
- 充分的单元测试和集成测试
- 分阶段实施，每个阶段都要验证功能

## 总结

推荐采用**方案一：完全隔离 + 协议抽象**，因为它：

1. 符合项目现有的架构模式
2. 提供清晰的职责分离
3. 易于维护和扩展
4. 遵循 SOLID 原则
5. 支持依赖注入和测试

这个方案将彻底解决当前的循环依赖问题，并为未来的扩展提供良好的基础。
